<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Authentication Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background-color: #1e40af;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .info {
            background-color: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication Setup Tool</h1>
        <p>This tool will create the mock authentication data for the Indusun project.</p>
        
        <div>
            <button onclick="setupAuth()">🚀 Setup Authentication Data</button>
            <button onclick="verifySetup()">🔍 Verify Current Users</button>
            <button onclick="clearResults()">🧹 Clear Results</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        async function setupAuth() {
            showResult('🚀 Setting up authentication data...', 'info');
            
            try {
                const response = await fetch('/api/setup-auth', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('✅ Setup successful!\n\n' + JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult('❌ Setup failed!\n\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('❌ Error: ' + error.message, 'error');
            }
        }
        
        async function verifySetup() {
            showResult('🔍 Verifying current users...', 'info');
            
            try {
                const response = await fetch('/api/setup-auth');
                const data = await response.json();
                
                if (response.ok) {
                    showResult('📊 Current users in database:\n\n' + JSON.stringify(data, null, 2), 'info');
                } else {
                    showResult('❌ Verification failed!\n\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('❌ Error: ' + error.message, 'error');
            }
        }
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // Auto-verify on page load
        window.onload = function() {
            verifySetup();
        };
    </script>
</body>
</html>
